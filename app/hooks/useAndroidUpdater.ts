import { useEffect, useState } from 'react';
import { Capacitor } from '@capacitor/core';
import { AppUpdate } from '@capawesome/capacitor-app-update';

interface UpdateInfo {
  version: string;
  url: string;
  releaseNotes?: string;
  mandatory?: boolean;
}

interface UpdateState {
  isChecking: boolean;
  updateAvailable: boolean;
  updateInfo: UpdateInfo | null;
  isDownloading: boolean;
  downloadProgress: number;
  error: string | null;
}

const UPDATE_CHECK_URL = 'https://your-r2-bucket.com/android-update.json';

export function useAndroidUpdater() {
  const [state, setState] = useState<UpdateState>({
    isChecking: false,
    updateAvailable: false,
    updateInfo: null,
    isDownloading: false,
    downloadProgress: 0,
    error: null,
  });

  // Only run on Android platform
  const isAndroid = Capacitor.getPlatform() === 'android';

  const getCurrentVersion = async (): Promise<string> => {
    try {
      const info = await AppUpdate.getAppUpdateInfo();
      return info.currentVersion;
    } catch (error) {
      console.error('Failed to get current version:', error);
      return '1.0.0'; // Fallback version
    }
  };

  const checkForUpdates = async (): Promise<void> => {
    if (!isAndroid) return;

    setState(prev => ({ ...prev, isChecking: true, error: null }));

    try {
      // Get current app version
      const currentVersion = await getCurrentVersion();
      
      // Fetch update info from R2
      const response = await fetch(UPDATE_CHECK_URL);
      if (!response.ok) {
        throw new Error(`Failed to check for updates: ${response.status}`);
      }

      const updateInfo: UpdateInfo = await response.json();
      
      // Compare versions (simple string comparison for now)
      const updateAvailable = updateInfo.version !== currentVersion;
      
      setState(prev => ({
        ...prev,
        isChecking: false,
        updateAvailable,
        updateInfo: updateAvailable ? updateInfo : null,
      }));

      console.log(`Version check: Current=${currentVersion}, Latest=${updateInfo.version}, Update=${updateAvailable}`);
      
    } catch (error) {
      console.error('Update check failed:', error);
      setState(prev => ({
        ...prev,
        isChecking: false,
        error: error instanceof Error ? error.message : 'Failed to check for updates',
      }));
    }
  };

  const downloadAndInstallUpdate = async (): Promise<void> => {
    if (!isAndroid || !state.updateInfo) return;

    setState(prev => ({ ...prev, isDownloading: true, downloadProgress: 0, error: null }));

    try {
      // Start the update download
      await AppUpdate.startFlexibleUpdate();
      
      // Monitor download progress
      const progressListener = await AppUpdate.addListener('onFlexibleUpdateStateUpdate', (state) => {
        if (state.installStatus === 'DOWNLOADING') {
          const progress = Math.round((state.bytesDownloaded / state.totalBytesToDownload) * 100);
          setState(prev => ({ ...prev, downloadProgress: progress }));
        } else if (state.installStatus === 'DOWNLOADED') {
          setState(prev => ({ ...prev, isDownloading: false, downloadProgress: 100 }));
          // Complete the installation
          AppUpdate.completeFlexibleUpdate();
        }
      });

      // Clean up listener after use
      setTimeout(() => {
        progressListener.remove();
      }, 30000); // 30 second timeout

    } catch (error) {
      console.error('Update download failed:', error);
      setState(prev => ({
        ...prev,
        isDownloading: false,
        error: error instanceof Error ? error.message : 'Failed to download update',
      }));
    }
  };

  const installImmediateUpdate = async (): Promise<void> => {
    if (!isAndroid || !state.updateInfo) return;

    try {
      // For immediate updates, the app will restart automatically
      await AppUpdate.performImmediateUpdate();
    } catch (error) {
      console.error('Immediate update failed:', error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to install update',
      }));
    }
  };

  // Auto-check for updates on mount
  useEffect(() => {
    if (isAndroid) {
      checkForUpdates();
    }
  }, [isAndroid]);

  return {
    ...state,
    isAndroid,
    checkForUpdates,
    downloadAndInstallUpdate,
    installImmediateUpdate,
  };
}
